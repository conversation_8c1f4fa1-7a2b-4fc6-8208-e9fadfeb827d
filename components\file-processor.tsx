"use client";

import { useState, useEffect } from "react";
import { FileTree, FileNode } from "./file-tree";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Play,
  Loader2,
  RefreshCw,
  Settings,
  Save,
  X,
  AudioWaveform,
  Video,
  Network,
  FileText,
  CreditCard,
  HelpCircle,
  Plus,
  History,
  Clock
} from "lucide-react";
import ReactMarkdown from "react-markdown";
import { cn } from "@/lib/utils";

// 规则模板定义
interface RuleTemplate {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  rule: string;
}

const ruleTemplates: RuleTemplate[] = [
  {
    id: "revise",
    name: "内容修订",
    icon: <FileText className="h-4 w-4" />,
    color: "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
    description: "修订和改进内容",
    rule: "revise",
  },
  {
    id: "anki",
    name: "Anki 闪卡",
    icon: <CreditCard className="h-4 w-4" />,
    color: "bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400",
    description: "生成 Anki 闪卡",
    rule: "anki",
  },
  {
    id: "audio",
    name: "音频概述",
    icon: <AudioWaveform className="h-4 w-4" />,
    color: "bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
    description: "生成音频概述内容",
    rule: "audio-overview",
  },
  {
    id: "video",
    name: "视频概述",
    icon: <Video className="h-4 w-4" />,
    color: "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400",
    description: "生成视频概述内容",
    rule: "video-overview",
  },
  {
    id: "mindmap",
    name: "思维导图",
    icon: <Network className="h-4 w-4" />,
    color: "bg-pink-100 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400",
    description: "生成思维导图",
    rule: "mindmap",
  },
  {
    id: "quiz",
    name: "测验题目",
    icon: <HelpCircle className="h-4 w-4" />,
    color: "bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400",
    description: "生成测验题目",
    rule: "quiz",
  },
];

// 处理任务记录
interface ProcessTask {
  id: string;
  fileName: string;
  rule: string;
  status: "pending" | "processing" | "completed" | "error";
  startTime: Date;
  endTime?: Date;
  originalContent?: string;
  generatedContent?: string;
  error?: string;
  message?: string;
  outputPath?: string;
}

export function FileProcessor() {
  const [inputFiles, setInputFiles] = useState<FileNode[]>([]);
  const [outputFiles, setOutputFiles] = useState<FileNode[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [selectedRule, setSelectedRule] = useState<string>("revise");
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [previewFile, setPreviewFile] = useState<{ name: string; content: string } | null>(null);

  // 规则配置相关状态
  const [showRuleConfig, setShowRuleConfig] = useState(false);
  const [customRuleName, setCustomRuleName] = useState("");
  const [customRuleContent, setCustomRuleContent] = useState("");
  const [isEditingRule, setIsEditingRule] = useState(false);
  const [customRules, setCustomRules] = useState<Array<{name: string, displayName: string, description: string}>>([]);

  // 处理任务相关状态
  const [processTasks, setProcessTasks] = useState<ProcessTask[]>([]);
  const [showTaskDialog, setShowTaskDialog] = useState(false);
  const [allTasksCompleted, setAllTasksCompleted] = useState(false);

  // 历史记录相关状态
  const [processHistory, setProcessHistory] = useState<ProcessTask[]>([]);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  // 加载文件树和规则列表
  useEffect(() => {
    loadFileTrees();
    loadCustomRules();
  }, []);

  const loadFileTrees = async () => {
    try {
      const [inputRes, outputRes] = await Promise.all([
        fetch("/api/files/input"),
        fetch("/api/files/output"),
      ]);

      if (inputRes.ok) {
        const inputData = await inputRes.json();
        setInputFiles(inputData.files || []);
      }

      if (outputRes.ok) {
        const outputData = await outputRes.json();
        setOutputFiles(outputData.files || []);
      }
    } catch (error) {
      console.error("加载文件树失败:", error);
    }
  };

  // 加载自定义规则列表
  const loadCustomRules = async () => {
    try {
      const response = await fetch("/api/rules");
      if (response.ok) {
        const data = await response.json();
        // 过滤掉与预设规则重复的自定义规则
        const presetRuleNames = ruleTemplates.map(t => t.rule);
        const filteredRules = (data.rules || []).filter((rule: any) =>
          !presetRuleNames.includes(rule.name)
        );
        setCustomRules(filteredRules);
      }
    } catch (error) {
      console.error("加载自定义规则失败:", error);
    }
  };

  // 刷新文件树
  const handleRefreshFiles = async () => {
    await loadFileTrees();
  };

  // 加载规则配置
  const loadRuleConfig = async (ruleName: string) => {
    try {
      const response = await fetch(`/api/rules/${ruleName}`);
      if (response.ok) {
        const data = await response.json();
        setCustomRuleContent(JSON.stringify(data, null, 2));
      } else {
        // 如果规则不存在，创建默认规则
        const defaultRule = {
          model: "gemini-2.0-flash-exp",
          config: {
            systemInstruction: `你是一个专业的内容生成助手。`,
          }
        };
        setCustomRuleContent(JSON.stringify(defaultRule, null, 2));
      }
    } catch (error) {
      console.error("加载规则失败:", error);
    }
  };

  // 保存自定义规则
  const handleSaveCustomRule = async () => {
    if (!customRuleName.trim()) {
      alert("请输入规则名称");
      return;
    }

    try {
      const ruleData = JSON.parse(customRuleContent);
      const response = await fetch(`/api/rules/${customRuleName}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(ruleData),
      });

      if (response.ok) {
        alert("规则保存成功！");
        setSelectedRule(customRuleName);
        setShowRuleConfig(false);
        setIsEditingRule(false);
        // 重新加载自定义规则列表
        await loadCustomRules();
      } else {
        alert("保存失败");
      }
    } catch (error) {
      console.error("保存规则失败:", error);
      alert("保存失败：JSON 格式错误");
    }
  };

  const handleProcess = async () => {
    if (selectedFiles.size === 0) {
      alert("请至少选择一个文件");
      return;
    }

    if (!selectedRule) {
      alert("请选择处理规则");
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    // 初始化处理任务
    const tasks: ProcessTask[] = Array.from(selectedFiles).map(filePath => ({
      id: `${Date.now()}-${Math.random()}`,
      fileName: filePath.split('/').pop() || filePath,
      rule: selectedRule,
      status: "pending",
      startTime: new Date(),
    }));

    setProcessTasks(tasks);
    setShowTaskDialog(true);
    setAllTasksCompleted(false);

    try {
      const response = await fetch("/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          files: Array.from(selectedFiles),
          rule: selectedRule,
        }),
      });

      if (!response.ok) {
        throw new Error("处理失败");
      }

      // 使用 Server-Sent Events 接收进度更新
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = JSON.parse(line.slice(6));

              if (data.type === "progress") {
                setProgress((data.completed / data.total) * 100);

                // 更新任务状态
                setProcessTasks(prev => prev.map(task => {
                  if (task.fileName === data.fileName) {
                    return {
                      ...task,
                      status: data.status,
                      originalContent: data.originalContent,
                      generatedContent: data.generatedContent,
                      error: data.error,
                      message: data.message,
                      outputPath: data.outputPath,
                      endTime: data.status === "completed" || data.status === "error" ? new Date() : undefined,
                    };
                  }
                  return task;
                }));
              } else if (data.type === "complete") {
                setProgress(100);
                setAllTasksCompleted(true);
                // 重新加载输出文件树
                await loadFileTrees();
                // 将完成的任务添加到历史记录
                setProcessHistory(prev => [...prev, ...processTasks]);
                // 处理完成后延迟自动关闭任务弹窗
                setTimeout(() => {
                  setShowTaskDialog(false);
                  setAllTasksCompleted(false);
                }, 3000); // 3秒后自动关闭
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("处理失败:", error);
      alert("处理失败，请查看控制台");

      // 更新所有任务为错误状态
      setProcessTasks(prev => prev.map(task => ({
        ...task,
        status: "error",
        error: error instanceof Error ? error.message : "未知错误",
        endTime: new Date(),
      })));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFilePreview = async (file: FileNode) => {
    try {
      const response = await fetch(`/api/files/content?path=${encodeURIComponent(file.path)}`);
      if (response.ok) {
        const data = await response.json();
        setPreviewFile({
          name: file.name,
          content: data.content,
        });
      }
    } catch (error) {
      console.error("加载文件内容失败:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input 文件树 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>输入文件</CardTitle>
                <CardDescription>选择要处理的文件</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshFiles}
                disabled={isProcessing}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <FileTree
              nodes={inputFiles}
              selectedFiles={selectedFiles}
              onSelectionChange={setSelectedFiles}
              mode="input"
            />
            <div className="mt-4">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedFiles.size} 个文件
              </span>
            </div>
          </CardContent>
        </Card>

        {/* 规则选择区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              处理规则
            </CardTitle>
            <CardDescription>选择或自定义内容处理规则</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {ruleTemplates.map((template) => (
                <div
                  key={template.id}
                  className={cn(
                    "p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md",
                    selectedRule === template.rule
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50",
                    template.color
                  )}
                  onClick={() => setSelectedRule(template.rule)}
                >
                  <div className="flex items-center gap-3">
                    {template.icon}
                    <div>
                      <div className="text-sm font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">{template.description}</div>
                    </div>
                  </div>
                </div>
              ))}

              {/* 自定义规则 */}
              {customRules.map((rule) => (
                <div
                  key={rule.name}
                  className={cn(
                    "p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md",
                    selectedRule === rule.name
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50",
                    "bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400"
                  )}
                  onClick={() => setSelectedRule(rule.name)}
                >
                  <div className="flex items-center gap-3">
                    <Settings className="h-4 w-4" />
                    <div>
                      <div className="text-sm font-medium">{rule.displayName}</div>
                      <div className="text-xs text-muted-foreground">{rule.description}</div>
                    </div>
                  </div>
                </div>
              ))}

              {/* 自定义规则按钮 */}
              <div
                className={cn(
                  "p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md",
                  "border-dashed border-muted-foreground/50 hover:border-primary/50",
                  "bg-muted/20 hover:bg-muted/40"
                )}
                onClick={() => {
                  setShowRuleConfig(true);
                  setIsEditingRule(true);
                  setCustomRuleName("");
                  setCustomRuleContent(JSON.stringify({
                    model: "gemini-2.0-flash-exp",
                    config: {
                      systemInstruction: "你是一个专业的内容生成助手。",
                    }
                  }, null, 2));
                }}
              >
                <div className="flex items-center gap-3 text-muted-foreground">
                  <Plus className="h-4 w-4" />
                  <div>
                    <div className="text-sm font-medium">自定义规则</div>
                    <div className="text-xs">创建新的处理规则</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 当前选中规则的描述和操作 */}
            {selectedRule && (
              <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">
                      当前规则: {
                        ruleTemplates.find(t => t.rule === selectedRule)?.name ||
                        customRules.find(r => r.name === selectedRule)?.displayName ||
                        selectedRule
                      }
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {
                        ruleTemplates.find(t => t.rule === selectedRule)?.description ||
                        customRules.find(r => r.name === selectedRule)?.description ||
                        "自定义规则"
                      }
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHistoryDialog(true)}
                    >
                      <History className="h-4 w-4 mr-1" />
                      历史
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowRuleConfig(true);
                        setIsEditingRule(false);
                        loadRuleConfig(selectedRule);
                      }}
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      配置
                    </Button>
                    <Button
                      onClick={handleProcess}
                      disabled={isProcessing || selectedFiles.size === 0 || !selectedRule}
                      size="sm"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          处理中...
                        </>
                      ) : (
                        <>
                          <Play className="mr-2 h-4 w-4" />
                          开始处理
                        </>
                      )}
                    </Button>
                  </div>
                </div>
                {isProcessing && (
                  <div className="mt-3">
                    <Progress value={progress} />
                    <p className="text-sm text-muted-foreground mt-2 text-center">
                      {Math.round(progress)}%
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Output 文件树 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>输出文件</CardTitle>
                <CardDescription>点击已完成的文件预览内容</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshFiles}
                disabled={isProcessing}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <FileTree
              nodes={outputFiles}
              selectedFiles={new Set()}
              onFileClick={handleFilePreview}
              mode="output"
            />
          </CardContent>
        </Card>
      </div>

      {/* 规则配置对话框 */}
      <Dialog open={showRuleConfig} onOpenChange={setShowRuleConfig}>
        <DialogContent className="max-w-3xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>
              {isEditingRule ? "创建自定义规则" : "查看规则配置"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {isEditingRule && (
              <div className="space-y-2">
                <Label htmlFor="rule-name">规则名称</Label>
                <input
                  id="rule-name"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  placeholder="输入规则名称"
                  value={customRuleName}
                  onChange={(e) => setCustomRuleName(e.target.value)}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="rule-config">规则配置 (JSON)</Label>
              <Textarea
                id="rule-config"
                className="h-96 font-mono text-sm"
                value={customRuleContent}
                onChange={(e) => setCustomRuleContent(e.target.value)}
                readOnly={!isEditingRule}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowRuleConfig(false)}>
                取消
              </Button>
              {isEditingRule && (
                <Button onClick={handleSaveCustomRule}>
                  <Save className="mr-2 h-4 w-4" />
                  保存规则
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 处理任务浮窗 */}
      <Dialog open={showTaskDialog} onOpenChange={setShowTaskDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>处理任务详情</span>
              {allTasksCompleted && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-green-600 dark:text-green-400">✓ 全部完成</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowTaskDialog(false);
                      setAllTasksCompleted(false);
                    }}
                  >
                    关闭
                  </Button>
                </div>
              )}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 max-h-[60vh] overflow-y-auto">
            {processTasks.map((task) => (
              <Card key={task.id} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "w-3 h-3 rounded-full",
                      task.status === "pending" && "bg-gray-400",
                      task.status === "processing" && "bg-blue-500 animate-pulse",
                      task.status === "completed" && "bg-green-500",
                      task.status === "error" && "bg-red-500"
                    )} />
                    <span className="font-medium">{task.fileName}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {task.status === "pending" && "等待中"}
                    {task.status === "processing" && "处理中"}
                    {task.status === "completed" && "已完成"}
                    {task.status === "error" && "失败"}
                  </span>
                </div>

                <div className="text-xs text-muted-foreground mb-2">
                  规则: {task.rule} | 开始时间: {task.startTime.toLocaleTimeString()}
                  {task.endTime && ` | 结束时间: ${task.endTime.toLocaleTimeString()}`}
                  {task.outputPath && ` | 输出路径: ${task.outputPath}`}
                </div>

                {task.message && (
                  <div className={cn(
                    "p-2 rounded text-sm mb-2",
                    task.status === "error"
                      ? "bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400"
                      : task.status === "completed"
                      ? "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                      : "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  )}>
                    {task.message}
                  </div>
                )}

                {task.error && (
                  <div className="p-2 bg-red-50 dark:bg-red-900/20 rounded text-sm text-red-600 dark:text-red-400">
                    错误详情: {task.error}
                  </div>
                )}

                {task.originalContent && (
                  <details className="mt-2">
                    <summary className="text-sm font-medium cursor-pointer">原始内容</summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                      {task.originalContent.substring(0, 500)}
                      {task.originalContent.length > 500 && "..."}
                    </pre>
                  </details>
                )}

                {task.generatedContent && (
                  <details className="mt-2">
                    <summary className="text-sm font-medium cursor-pointer">生成内容</summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                      {task.generatedContent.substring(0, 500)}
                      {task.generatedContent.length > 500 && "..."}
                    </pre>
                  </details>
                )}
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* 历史记录对话框 */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
        <DialogContent className="max-w-6xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              处理历史记录
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 max-h-[60vh] overflow-y-auto">
            {processHistory.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                暂无处理记录
              </div>
            ) : (
              processHistory.map((task) => (
                <Card key={task.id} className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "w-3 h-3 rounded-full",
                        task.status === "completed" && "bg-green-500",
                        task.status === "error" && "bg-red-500"
                      )} />
                      <span className="font-medium">{task.fileName}</span>
                      <span className="text-xs bg-muted px-2 py-1 rounded">{task.rule}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {task.startTime.toLocaleString()}
                    </span>
                  </div>

                  {task.outputPath && (
                    <div className="text-xs text-muted-foreground mb-2">
                      输出路径: {task.outputPath}
                    </div>
                  )}

                  {task.message && (
                    <div className={cn(
                      "p-2 rounded text-sm",
                      task.status === "error"
                        ? "bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400"
                        : "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                    )}>
                      {task.message}
                    </div>
                  )}
                </Card>
              ))
            )}
          </div>

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setProcessHistory([])}
              disabled={processHistory.length === 0}
            >
              清空历史
            </Button>
            <Button variant="outline" onClick={() => setShowHistoryDialog(false)}>
              关闭
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 文件预览对话框 */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{previewFile?.name}</DialogTitle>
          </DialogHeader>
          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown>{previewFile?.content || ""}</ReactMarkdown>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

