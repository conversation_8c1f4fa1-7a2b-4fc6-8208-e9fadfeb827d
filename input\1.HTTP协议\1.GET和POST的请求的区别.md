### 1. GET和POST的请求的区别



Post 和 Get 是 HTTP 请求的两种方法，其区别如下：



**应用场景**：GET 请求是一个幂等的请求，一般 Get 请求用于对服务器资源不会产生影响的场景，比如说请求一个网页的资源。而 Post 不是一个幂等的请求，一般用于对服务器资源会产生影响的情景，比如注册用户这一类的操作。  
**是否缓存**：因为两者应用场景不同，浏览器一般会对 Get 请求缓存，但很少对 Post 请求缓存。  
发送的报文格式：Get 请求的报文中实体部分为空，Post 请求的报文中实体部分一般为向服务器发送的数据。  
**安全性**：Get 请求可以将请求的参数放入 url 中向服务器发送，这样的做法相对于 Post 请求来说是不太安全的，因为请求的 url 会被保留在历史记录中。  
**请求长度**：浏览器由于对 url 长度的限制，所以会影响 get 请求发送数据时的长度。这个限制是浏览器规定的，并不是 RFC 规定的。  
**参数类型**：post 的参数传递支持更多的数据类型。